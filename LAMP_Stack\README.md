# ProjectLAMP - LAMP Stack on AWS EC2

Complete LAMP (Linux, Apache, MySQL, PHP) stack implementation on AWS EC2 without Docker.

## Quick Setup

### 1. EC2 Instance Setup
```bash
# Launch Ubuntu 20.04 LTS instance
# Security Groups: SSH (22), HTTP (80), MySQL (3306)
ssh -i your-key.pem ubuntu@your-ec2-ip
```

### 2. Install LAMP Stack
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Apache
sudo apt install apache2 -y
sudo systemctl enable apache2

# Install MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# Install PHP
sudo apt install php libapache2-mod-php php-mysql -y
```

### 3. Create Virtual Host
```bash
# Create project directory
sudo mkdir /var/www/projectlamp
sudo chown -R $USER:$USER /var/www/projectlamp

# Create virtual host config
sudo nano /etc/apache2/sites-available/projectlamp.conf
```

```apache
<VirtualHost *:80>
    ServerName projectlamp
    DocumentRoot /var/www/projectlamp
    <Directory /var/www/projectlamp>
        AllowOverride All
        Require all granted
    </Directory>
    ErrorLog ${APACHE_LOG_DIR}/projectlamp_error.log
    CustomLog ${APACHE_LOG_DIR}/projectlamp_access.log combined
</VirtualHost>
```

```bash
# Enable site
sudo a2ensite projectlamp.conf
sudo a2dissite 000-default.conf
sudo a2enmod rewrite
sudo systemctl reload apache2
```

### 4. Configure Database
```bash
mysql -u root -p
```


### 5. Test Installation
```bash
# Create test PHP file
nano /var/www/projectlamp/index.php
```

```php
<?php
$conn = new mysqli("localhost", "projectlamp_user", "your_password", "projectlamp_db");
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
echo "<h1>ProjectLAMP Working!</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Database: Connected successfully</p>";
?>
```

## Access Your Site
```
http://your-ec2-public-ip
```

## Key Commands
```bash
# Restart services
sudo systemctl restart apache2
sudo systemctl restart mysql

# Check logs
sudo tail -f /var/log/apache2/projectlamp_error.log

# Set permissions
sudo chown -R www-data:www-data /var/www/projectlamp
sudo chmod -R 755 /var/www/projectlamp
```

