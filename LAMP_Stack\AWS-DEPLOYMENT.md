# ProjectLAP AWS EC2 Deployment Guide

## EC2 Instance Setup

### 1. Launch EC2 Instance
```bash
# Instance specifications
Instance Type: t2.micro 
AMI: Amazon Linux 2
Storage: 8GB+ EBS volume
```

### 2. Security Group Configuration
```bash
# Inbound rules
SSH (22): Your IP
HTTP (80): 0.0.0.0/0
MySQL (3306): Your IP (optional)
```

### 3. Connect to Instance
```bash
ssh -i your-key.pem ec2-user@your-ec2-public-ip
```

